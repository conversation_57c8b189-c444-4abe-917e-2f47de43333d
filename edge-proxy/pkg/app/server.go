package server

import (
	"fmt"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	boxhttp "github.com/EPIKio/myepikV2/edge/pkg/boxes/http"
	"github.com/EPIKio/myepikV2/edge/pkg/grpc"
	"github.com/EPIKio/myepikV2/edge/pkg/proxy"
	"github.com/EPIKio/myepikV2/edge/pkg/queue"
)

// Config contains server configuration options.
type Config struct {
	Port          int
	BoxRepository boxes.BoxFinder // Changed to interface
	QueueManager  queue.QueueManager
}

// Server encapsulates all dependencies for the edge device proxy service.
type Server struct {
	grpcServer *grpc.Server
}

// New creates and configures a new server instance.
func New(cfg Config) (*Server, error) {
	// Create HTTP client.
	client := boxhttp.NewDefaultClient()

	// Create proxy server with both HTTP client and box repository.
	proxyServer, err := proxy.NewServer(client, cfg.BoxRepository)
	if err != nil {
		return nil, fmt.Errorf("failed to create proxy server: %w", err)
	}

	// Create gRPC server.
	grpcServer, err := grpc.New(grpc.Config{
		Port:          cfg.Port,
		BoxRepository: cfg.BoxRepository,
		ProxyServer:   proxyServer,
		QueueManager:  cfg.QueueManager,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create gRPC server: %w", err)
	}

	s := &Server{
		grpcServer: grpcServer,
	}

	return s, nil
}

// Run starts the server and blocks until it's stopped or fails.
func (s *Server) Run() error {
	return s.grpcServer.Run()
}

// Stop gracefully shuts down the server.
func (s *Server) Stop() {
	if s.grpcServer != nil {
		s.grpcServer.Stop()
	}
}
