package bdd

import "github.com/smartystreets/goconvey/convey"

// Common assertions
var (
	// Equality
	ShouldEqual          = convey.ShouldEqual
	ShouldNotEqual       = convey.ShouldNotEqual
	ShouldAlmostEqual    = convey.ShouldAlmostEqual
	ShouldNotAlmostEqual = convey.ShouldNotAlmostEqual
	ShouldResemble       = convey.ShouldResemble
	ShouldNotResemble    = convey.ShouldNotResemble
	ShouldPointTo        = convey.ShouldPointTo
	ShouldNotPointTo     = convey.ShouldNotPointTo
	ShouldBeNil          = convey.ShouldBeNil
	ShouldNotBeNil       = convey.ShouldNotBeNil
	ShouldBeTrue         = convey.ShouldBeTrue
	ShouldBeFalse        = convey.ShouldBeFalse
	ShouldBeZeroValue    = convey.ShouldBeZeroValue

	// Numeric comparison
	ShouldBeGreaterThan          = convey.ShouldBeGreaterThan
	ShouldBeGreaterThanOrEqualTo = convey.ShouldBeGreaterThanOrEqualTo
	ShouldBeLessThan             = convey.ShouldBeLessThan
	ShouldBeLessThanOrEqualTo    = convey.ShouldBeLessThanOrEqualTo
	ShouldBeBetween              = convey.ShouldBeBetween
	ShouldNotBeBetween           = convey.ShouldNotBeBetween
	ShouldBeBetweenOrEqual       = convey.ShouldBeBetweenOrEqual
	ShouldNotBeBetweenOrEqual    = convey.ShouldNotBeBetweenOrEqual

	// Collections
	ShouldContain             = convey.ShouldContain
	ShouldNotContain          = convey.ShouldNotContain
	ShouldContainKey          = convey.ShouldContainKey
	ShouldNotContainKey       = convey.ShouldNotContainKey
	ShouldBeIn                = convey.ShouldBeIn
	ShouldNotBeIn             = convey.ShouldNotBeIn
	ShouldBeEmpty             = convey.ShouldBeEmpty
	ShouldNotBeEmpty          = convey.ShouldNotBeEmpty
	ShouldHaveLength          = convey.ShouldHaveLength
	ShouldStartWith           = convey.ShouldStartWith
	ShouldNotStartWith        = convey.ShouldNotStartWith
	ShouldEndWith             = convey.ShouldEndWith
	ShouldNotEndWith          = convey.ShouldNotEndWith
	ShouldBeBlank             = convey.ShouldBeBlank
	ShouldNotBeBlank          = convey.ShouldNotBeBlank
	ShouldContainSubstring    = convey.ShouldContainSubstring
	ShouldNotContainSubstring = convey.ShouldNotContainSubstring

	// Panics
	ShouldPanic        = convey.ShouldPanic
	ShouldNotPanic     = convey.ShouldNotPanic
	ShouldPanicWith    = convey.ShouldPanicWith
	ShouldNotPanicWith = convey.ShouldNotPanicWith

	// Type checking
	ShouldHaveSameTypeAs    = convey.ShouldHaveSameTypeAs
	ShouldNotHaveSameTypeAs = convey.ShouldNotHaveSameTypeAs
	ShouldImplement         = convey.ShouldImplement
	ShouldNotImplement      = convey.ShouldNotImplement

	// Time
	ShouldHappenBefore         = convey.ShouldHappenBefore
	ShouldHappenOnOrBefore     = convey.ShouldHappenOnOrBefore
	ShouldHappenAfter          = convey.ShouldHappenAfter
	ShouldHappenOnOrAfter      = convey.ShouldHappenOnOrAfter
	ShouldHappenBetween        = convey.ShouldHappenBetween
	ShouldHappenOnOrBetween    = convey.ShouldHappenOnOrBetween
	ShouldNotHappenOnOrBetween = convey.ShouldNotHappenOnOrBetween
	ShouldHappenWithin         = convey.ShouldHappenWithin
	ShouldNotHappenWithin      = convey.ShouldNotHappenWithin

	// Error checking
	ShouldBeError = convey.ShouldBeError
	ShouldSucceed = convey.ShouldBeNil // Alias for checking error is nil
)
