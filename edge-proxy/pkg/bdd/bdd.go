package bdd

import (
	"fmt"
	"runtime"
	"strings"
	"sync"
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"github.com/smartystreets/goconvey/convey/reporting"
)

var (
	currentT   *testing.T
	tMutex     sync.RWMutex
	scopeStack []string
)

func init() {
	reporting.QuietMode()
	reporting.SuppressConsoleStatistics()
	reporting.BuildSilentReporter()
}

// setCurrentT safely sets the current testing.T
func setCurrentT(t *testing.T) {
	tMutex.Lock()
	currentT = t
	tMutex.Unlock()
	// Reset context for new test
	scopeStack = make([]string, 0)
}

// getCurrentT safely gets the current testing.T
func getCurrentT() *testing.T {
	tMutex.RLock()
	defer tMutex.RUnlock()
	return currentT
}

// Scenario describes a complete test scenario.
func Scenario(description string, t *testing.T, action func()) {
	setCurrentT(t)
	scopeStack = append(scopeStack, "Scenario: "+description)
	convey.Convey("Scenario: "+description, t, action)
}

// Given describes the test context.
func Given(description string, action func()) {
	scopeStack = append(scopeStack, "Given "+description)
	convey.Convey("Given "+description, action)
}

// When describes an action or event.
func When(description string, action func()) {
	scopeStack = append(scopeStack, "When "+description)
	convey.Convey("When "+description, action)
}

// Then describes an expected outcome.
func Then(description string, action func()) {
	scopeStack = append(scopeStack, "Then "+description)
	convey.Convey("Then "+description, action)
}

// Check works like convey.So but with better error reporting
func Check(actual interface{}, assertion convey.Assertion, expected ...interface{}) {
	if message := assertion(actual, expected...); message != "" {
		_, file, line, _ := runtime.Caller(1)

		// Build failure message with BDD context
		var fullMessage strings.Builder
		fullMessage.WriteString(fmt.Sprintf("\n%s:%d\n", file, line))

		// Add BDD context
		if len(scopeStack) > 0 {
			fullMessage.WriteString("\nTest Context:\n")
			indent := ""
			for _, scope := range scopeStack {
				fullMessage.WriteString(indent + scope + "\n")
				indent += "  "
			}
			fullMessage.WriteString("\n")
		}

		fullMessage.WriteString("Assertion failed: " + message + "\n")

		getCurrentT().Fatal(fullMessage.String()) // Using Fatal instead of Fatalf
	}
}
