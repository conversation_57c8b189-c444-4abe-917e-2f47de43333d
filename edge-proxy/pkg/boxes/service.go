package boxes

const (
	// Main HTTP service port on edge devices.
	RestbinPort = 9988

	// Monitoring and repair agent's HTTP server.
	WatchguardPort = 9081
)

// ServicePort specifies which edge device service to contact.
type ServicePort int

const (
	Restbin    ServicePort = RestbinPort
	Watchguard ServicePort = WatchguardPort
)

// String provides a human-readable name for each service.
func (s ServicePort) String() string {
	switch s {
	case Restbin:
		return "Restbin"
	case Watchguard:
		return "Watchguard"
	default:
		return "Unknown Service"
	}
}
