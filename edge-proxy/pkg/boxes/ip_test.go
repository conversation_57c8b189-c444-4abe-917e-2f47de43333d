package boxes_test

import (
	"testing"

	. "github.com/EPIKio/myepikV2/edge/pkg/bdd"
	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
)

func TestToDatacenterIP(t *testing.T) {
	Scenario("Converting LA datacenter IP addresses to other datacenter ranges", t, func() {
		Given("an IP meant to stay in the Los Angeles datacenter", func() {
			When("converting an LA primary range address", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "LA")

				Then("it should remain in LA's range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, ip)
				})
			})

			When("converting an LA secondary range address", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "LA")

				Then("it should remain in LA's range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, ip)
				})
			})
		})

		Given("translating an LA IP to the Chicago datacenter", func() {
			When("converting from LA's primary range", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "CH")

				Then("it should map to Chicago's corresponding range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, "*********")
				})
			})

			When("converting from LA's secondary range", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "CH")

				Then("it should map to Chicago's corresponding range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, "*********")
				})
			})
		})

		Given("translating an LA IP to the New York datacenter", func() {
			When("converting from LA's primary range", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "NY")

				Then("it should map to New York's corresponding range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, "*********")
				})
			})

			When("converting from LA's secondary range", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "NY")

				Then("it should map to New York's corresponding range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, "*********")
				})
			})
		})

		Given("translating an LA IP to the Atlanta datacenter", func() {
			When("converting from LA's primary range", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "AT")

				Then("it should map to Atlanta's corresponding range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, "*********")
				})
			})

			When("converting from LA's secondary range", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "AT")

				Then("it should map to Atlanta's corresponding range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, "*********")
				})
			})
		})

		Given("translating an LA IP to the Dallas datacenter", func() {
			When("using the DA datacenter code", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "DA")

				Then("it should map to Dallas's range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, "*********")
				})
			})

			When("using the alternative DL code", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "DL")

				Then("it should map to the same Dallas range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, "*********")
				})
			})

			When("using the alternative DAL code", func() {
				ip := "*********"
				translated, err := boxes.ToDatacenterIP(ip, "DAL")

				Then("it should map to the same Dallas range", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, "*********")
				})
			})
		})

		Given("non-LA IP addresses", func() {
			When("given a private network address outside the 10.x range", func() {
				ip := "***********"
				translated, err := boxes.ToDatacenterIP(ip, "NY")

				Then("it should pass through unchanged", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, ip)
				})
			})

			When("given a 10.x address outside LA's VPN ranges", func() {
				ip := "**********"
				translated, err := boxes.ToDatacenterIP(ip, "NY")

				Then("it should pass through unchanged", func() {
					Check(err, ShouldBeNil)
					Check(translated, ShouldEqual, ip)
				})
			})
		})

		Given("handling invalid inputs", func() {
			When("given an incomplete IP address", func() {
				ip := "10.15.1"
				translated, err := boxes.ToDatacenterIP(ip, "NY")

				Then("it should return an error", func() {
					Check(err, ShouldEqual, boxes.ErrInvalidIP)
					Check(translated, ShouldEqual, "")
				})
			})

			When("given a malformed IP address", func() {
				ip := "10.abc.1.1"
				translated, err := boxes.ToDatacenterIP(ip, "NY")

				Then("it should return an error", func() {
					Check(err, ShouldEqual, boxes.ErrInvalidIP)
					Check(translated, ShouldEqual, "")
				})
			})
		})
	})
}
