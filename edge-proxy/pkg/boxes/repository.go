package boxes

import (
	"context"
	"fmt"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// Ensure MongoBoxRepository implements BoxFinder
var _ BoxFinder = (*MongoBoxRepository)(nil)

// MongoBoxRepository provides MongoDB-backed box lookups
type MongoBoxRepository struct {
	boxes *mongo.Collection
}

func NewBoxRepository(ctx context.Context) (*MongoBoxRepository, error) {
	// Add timeout for initial connection
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return &MongoBoxRepository{
		boxes: db.DB.Collection("epikboxes"),
	}, nil
}

// FindBySerialNumber implements BoxFinder
func (r *MongoBoxRepository) FindBySerialNumber(ctx context.Context, serialNumber string) (*Box, error) {
	// Add timeout for find operation
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	var box Box
	err := r.boxes.FindOne(ctx, bson.M{"serialNumber": serialNumber}).Decode(&box)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, fmt.Errorf("error finding box by serial number: %w", err)
	}

	return &box, nil
}
