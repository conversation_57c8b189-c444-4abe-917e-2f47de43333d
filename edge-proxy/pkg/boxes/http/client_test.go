package http_test

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	. "github.com/EPIKio/myepikV2/edge/pkg/bdd"
	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	boxhttp "github.com/EPIKio/myepikV2/edge/pkg/boxes/http"
)

// mockEngine simulates real HTTP responses and implements the Requester interface.
type mockEngine struct {
	response  *http.Response
	err       error
	lastReq   *http.Request
	delay     time.Duration // Simulates network delay
	callCount int           // Count the number of calls for retry testing

	// doFunc allows customizing the behavior of Do in tests.
	doFunc func(req *http.Request) (*http.Response, error)
}

// Do implements the Requester interface.
func (m *mockEngine) Do(req *http.Request) (*http.Response, error) {
	m.callCount++
	m.lastReq = req
	if m.delay > 0 {
		time.Sleep(m.delay)
	}
	if m.doFunc != nil {
		return m.doFunc(req)
	}
	return m.response, m.err
}

func TestClient(t *testing.T) {
	Scenario("Making HTTP requests to edge devices", t, func() {
		Given("a successful HTTP response", func() {
			engine := &mockEngine{
				response: &http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(strings.NewReader(`{"status":"ok"}`)),
					Header:     http.Header{"Content-Type": []string{"application/json"}},
				},
			}
			client := boxhttp.NewClient(engine, boxhttp.DefaultConfig)
			// Override sleep to avoid real delays.
			client.SetSleep(func(d time.Duration) {})

			When("sending a request", func() {
				resp, err := client.Request(context.Background(), "10.15.1.2", boxes.Restbin, "/test")

				Then("it should succeed", func() {
					Check(err, ShouldBeNil)
				})

				Then("it should construct the correct URL", func() {
					Check(engine.lastReq.URL.String(), ShouldEqual, "http://10.15.1.2:9988/test")
				})

				Then("it should return status 200", func() {
					Check(resp.StatusCode, ShouldEqual, 200)
				})

				Then("it should parse the response correctly", func() {
					Check(string(resp.Body), ShouldEqual, `{"status":"ok"}`)
				})
			})
		})

		Given("a timeout occurs", func() {
			engine := &mockEngine{err: context.DeadlineExceeded}
			client := boxhttp.NewClient(engine, boxhttp.DefaultConfig)
			client.SetSleep(func(d time.Duration) {})

			When("sending a request", func() {
				resp, err := client.Request(context.Background(), "10.15.1.2", boxes.Restbin, "/test")

				Then("it should return a timeout error", func() {
					Check(errors.Is(err, context.DeadlineExceeded), ShouldBeTrue)
				})

				Then("response should be nil", func() {
					Check(resp, ShouldBeNil)
				})
			})
		})

		Given("a network failure occurs", func() {
			engine := &mockEngine{err: errors.New("network failure")}
			client := boxhttp.NewClient(engine, boxhttp.DefaultConfig)
			client.SetSleep(func(d time.Duration) {})

			When("sending a request", func() {
				resp, err := client.Request(context.Background(), "10.15.1.2", boxes.Restbin, "/test")

				Then("it should return a network error", func() {
					Check(err.Error(), ShouldContainSubstring, "network failure")
				})

				Then("response should be nil", func() {
					Check(resp, ShouldBeNil)
				})
			})
		})

		Given("a transient error that succeeds on retry", func() {
			attemptsBeforeSuccess := 2
			// Start with an engine that returns an error.
			engine := &mockEngine{
				err: errors.New("transient error"),
			}
			client := boxhttp.NewClient(engine, boxhttp.Config{
				Timeout:               5 * time.Second,
				DialTimeout:           3 * time.Second,
				KeepAlive:             30 * time.Second,
				TLSHandshakeTimeout:   3 * time.Second,
				ResponseHeaderTimeout: 5 * time.Second,
				IdleConnTimeout:       90 * time.Second,
				MaxRetries:            3,
				RetryDelay:            10 * time.Millisecond,
			})
			client.SetSleep(func(d time.Duration) {}) // Override sleep to avoid delay.

			// Save original doFunc (if any) and use defer to restore it.
			originalDoFunc := engine.doFunc
			defer func() {
				engine.doFunc = originalDoFunc
			}()

			// Override behavior: first few calls fail, then succeed.
			engine.doFunc = func(req *http.Request) (*http.Response, error) {
				if engine.callCount < attemptsBeforeSuccess {
					return nil, errors.New("transient error")
				}
				return &http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(strings.NewReader(`{"status":"ok"}`)),
					Header:     http.Header{"Content-Type": []string{"application/json"}},
				}, nil
			}

			When("sending a request", func() {
				resp, err := client.Request(context.Background(), "10.15.1.2", boxes.Restbin, "/test")

				Then("it should eventually succeed", func() {
					Check(err, ShouldBeNil)
					Check(resp.StatusCode, ShouldEqual, 200)
					Check(engine.callCount, ShouldEqual, attemptsBeforeSuccess)
				})
			})
		})

		Given("a client configured with retries", func() {
			cfg := boxhttp.Config{
				MaxRetries: 3,
				RetryDelay: time.Second, // Duration doesn't matter since we mock sleep
			}

			When("requests fail with different errors", func() {
				attempts := 0
				sleepCalls := 0

				engine := &mockEngine{
					doFunc: func(req *http.Request) (*http.Response, error) {
						attempts++
						return nil, fmt.Errorf("attempt %d failed", attempts)
					},
				}

				client := boxhttp.NewClient(engine, cfg)
				client.SetSleep(func(d time.Duration) {
					sleepCalls++
				})

				_, err := client.Request(context.Background(), "10.0.0.1", boxes.Restbin, "/test")

				Then("it should make the correct number of attempts", func() {
					Check(attempts, ShouldEqual, 3)
					Check(sleepCalls, ShouldEqual, 2)
					Check(err, ShouldNotBeNil)
				})
			})
		})
	})
}
