package http

import (
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
)

// Requester abstracts an entity capable of executing HTTP requests.
type Requester interface {
	Do(req *http.Request) (*http.Response, error)
}

// Config holds configurable parameters for the HTTP client.
type Config struct {
	Timeout               time.Duration
	DialTimeout           time.Duration
	KeepAlive             time.Duration
	TLSHandshakeTimeout   time.Duration
	ResponseHeaderTimeout time.Duration
	IdleConnTimeout       time.Duration
	MaxRetries            int
	RetryDelay            time.Duration
}

// DefaultConfig provides reasonable defaults.
var DefaultConfig = Config{
	Timeout:               5 * time.Second,
	DialTimeout:           3 * time.Second,
	KeepAlive:             30 * time.Second,
	TLSHandshakeTimeout:   3 * time.Second,
	ResponseHeaderTimeout: 5 * time.Second,
	IdleConnTimeout:       90 * time.Second,
	MaxRetries:            0,                      // Set to > 0 if you want automatic retries
	RetryDelay:            100 * time.Millisecond, // Delay between retries
}

// Client wraps a Requester and provides higher-level methods for making HTTP requests.
type Client struct {
	requester Requester
	config    Config
	// sleep is a function that sleeps for a given duration.
	// In production, this is time.Sleep. In tests, it can be overridden to avoid real delays.
	sleep func(time.Duration)
}

// NewClient creates a new Client with the provided Requester and configuration.
// It uses time.Sleep by default.
func NewClient(requester Requester, config Config) *Client {
	return &Client{
		requester: requester,
		config:    config,
		sleep:     time.Sleep,
	}
}

// NewDefaultClient creates a Client using the default http.Client and DefaultConfig.
func NewDefaultClient() *Client {
	cfg := DefaultConfig
	httpClient := &http.Client{
		Timeout: cfg.Timeout,
		Transport: &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   cfg.DialTimeout,
				KeepAlive: cfg.KeepAlive,
			}).DialContext,
			TLSHandshakeTimeout:   cfg.TLSHandshakeTimeout,
			ResponseHeaderTimeout: cfg.ResponseHeaderTimeout,
			IdleConnTimeout:       cfg.IdleConnTimeout,
		},
	}
	return NewClient(httpClient, cfg)
}

// Request constructs and sends an HTTP GET request.
// It applies the configured retry logic and returns a parsed response.
func (c *Client) Request(
	ctx context.Context,
	ip string,
	port boxes.ServicePort,
	path string,
	_method ...string,
) (*boxes.Response, error) {
	var method string
	if len(_method) == 0 {
		method = "GET"
	} else {
		method = _method[0]
	}
	url := fmt.Sprintf("http://%s:%d%s", ip, int(port), path)
	req, err := http.NewRequestWithContext(ctx, method, url, nil)
	if err != nil {
		return nil, err
	}

	resp, _ := c.requester.Do(req)
	// If the context is done, no point retrying.
	if ctx.Err() != nil {
		return nil, fmt.Errorf("context error: %w", ctx.Err())
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("reading response body: %w", err)
	}

	headers := make(map[string]string)
	for k, v := range resp.Header {
		if len(v) > 0 {
			headers[k] = v[0]
		}
	}

	return &boxes.Response{
		StatusCode: resp.StatusCode,
		Body:       body,
		Headers:    headers,
	}, nil
}

// SetSleep allows overriding the sleep function.
// This is primarily useful for testing so that delays can be skipped.
func (c *Client) SetSleep(f func(time.Duration)) {
	c.sleep = f
}
