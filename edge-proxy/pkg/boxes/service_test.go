package boxes_test

import (
	"testing"

	. "github.com/EPIKio/myepikV2/edge/pkg/bdd"
	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
)

func TestServicePort(t *testing.T) {
	Scenario("Converting service ports to strings", t, func() {
		Given("a Restbin service port", func() {
			port := boxes.Restbin

			Then("it should have port number 9988", func() {
				Check(int(port), ShouldEqual, 9988)
			})

			Then("it should be identified as 'Restbin'", func() {
				Check(port.String(), ShouldEqual, "Restbin")
			})
		})

		Given("a Watchguard service port", func() {
			port := boxes.Watchguard

			Then("it should have port number 9081", func() {
				Check(int(port), ShouldEqual, 9081)
			})

			Then("it should be identified as 'Watchguard'", func() {
				Check(port.String(), ShouldEqual, "Watchguard")
			})
		})

		Given("an unknown service port", func() {
			port := boxes.ServicePort(1234)

			Then("it should be identified as 'Unknown Service'", func() {
				Check(port.String(), ShouldEqual, "Unknown Service")
			})
		})
	})
}
