package boxes

import (
	"fmt"
	"strconv"
	"strings"
)

// ErrInvalidIP is returned when the IP address format is invalid
var ErrInvalidIP = fmt.Errorf("invalid IP address format")

// ToDatacenterIP converts an IP address based on datacenter-specific VPN rules.
// Returns the translated IP address or an error if the input is invalid.
func ToDatacenterIP(ip, datacenter string) (string, error) {
	// LA is our reference datacenter - no translation needed
	if datacenter == "LA" {
		return ip, nil
	}

	// Parse IP address
	octets := strings.Split(ip, ".")
	if len(octets) != 4 {
		return "", ErrInvalidIP
	}

	// Convert octets to integers for comparison
	o1, err := strconv.Atoi(octets[0])
	if err != nil {
		return "", ErrInvalidIP
	}
	o2, err := strconv.Atoi(octets[1])
	if err != nil {
		return "", ErrInvalidIP
	}

	// Only translate VPN addresses (10.x.x.x)
	if o1 != 10 {
		return ip, nil
	}

	// Only translate addresses with second octet 15 or 64
	if o2 != 15 && o2 != 64 {
		return ip, nil
	}

	// Get the translation map for this second octet
	translations := getTranslations(o2)

	// Normalize Dallas datacenter codes
	if datacenter == "DA" || datacenter == "DL" || datacenter == "DAL" {
		datacenter = "DA"
	}

	// Look up the new second octet
	newO2, exists := translations[datacenter]
	if !exists {
		// If datacenter isn't in our translation map, return unchanged
		return ip, nil
	}

	// Return the translated IP
	return fmt.Sprintf("10.%d.%s.%s", newO2, octets[2], octets[3]), nil
}

// getTranslations returns the translation map for a given second octet
func getTranslations(secondOctet int) map[string]int {
	if secondOctet == 15 {
		return map[string]int{
			"CH": 18,
			"NY": 19,
			"AT": 20,
			"DA": 21,
		}
	}
	if secondOctet == 64 {
		return map[string]int{
			"CH": 66,
			"NY": 67,
			"AT": 68,
			"DA": 69,
		}
	}
	return map[string]int{}
}
