package boxes

import "context"

// HTTP response from an edge device.
type Response struct {
	StatusCode int
	Body       []byte
	Headers    map[string]string
}

// Makes HTTP requests to edge devices.
type EdgeDeviceClient interface {
	Request(
		ctx context.Context,
		ip string,
		port ServicePort,
		path string,
		_method ...string,
	) (*Response, error)
}

// BoxFinder looks up boxes by their properties
type BoxFinder interface {
	FindBySerialNumber(ctx context.Context, serialNumber string) (*Box, error)
}
