package grpc

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"net"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	"github.com/EPIKio/myepikV2/edge/pkg/proxy"
	"github.com/EPIKio/myepikV2/edge/pkg/queue"
	pb "github.com/EPIKio/myepikV2/edge/proto/edge/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

// Wraps the gRPC server with our service implementation.
type Server struct {
	pb.UnimplementedEdgeDeviceProxyServer
	proxyServer  *proxy.Server
	queueManager queue.QueueManager
	grpcServer   *grpc.Server
	listener     net.Listener
}

// Config contains server configuration.
type Config struct {
	Port          int
	BoxRepository boxes.BoxFinder // Changed to use BoxFinder interface
	ProxyServer   *proxy.Server
	QueueManager  queue.QueueManager
}

// Creates a new gRPC server instance.
func New(cfg Config) (*Server, error) {
	// Create listener.
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Port))
	if err != nil {
		return nil, fmt.Errorf("failed to listen: %w", err)
	}

	// Create gRPC server.
	grpcServer := grpc.NewServer()

	// Create server instance.
	s := &Server{
		proxyServer:  cfg.ProxyServer,
		queueManager: cfg.QueueManager,
		grpcServer:   grpcServer,
		listener:     lis,
	}

	// Register our service.
	pb.RegisterEdgeDeviceProxyServer(grpcServer, s)

	// Enable reflection (add just this one line)
	reflection.Register(grpcServer)

	return s, nil
}

// Starts the gRPC server.
func (s *Server) Run() error {
	return s.grpcServer.Serve(s.listener)
}

// Gracefully stops the gRPC server.
func (s *Server) Stop() {
	if s.grpcServer != nil {
		s.grpcServer.GracefulStop()
	}
}

// Implements the gRPC service method.
func (s *Server) HandleRequest(ctx context.Context, req *pb.DeviceRequest) (*pb.DeviceResponse, error) {
	// Check if this should be processed asynchronously
	if req.Async {
		return s.handleAsyncRequest(ctx, req)
	}

	// Process synchronously using existing proxy server
	statusCode, body, headers, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, req.Path)
	if err != nil {
		return nil, err // Error is already in the correct gRPC format
	}

	return &pb.DeviceResponse{
		StatusCode: statusCode,
		Body:       body,
		Headers:    headers,
		IsAsync:    false,
	}, nil
}

// handleAsyncRequest processes a request asynchronously
func (s *Server) handleAsyncRequest(ctx context.Context, req *pb.DeviceRequest) (*pb.DeviceResponse, error) {
	// Generate unique request ID
	requestID, err := generateRequestID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate request ID: %w", err)
	}

	// Convert protobuf priority to queue priority
	priority := convertPriority(req.Priority)

	// Create queued request
	queuedReq := &queue.QueuedRequest{
		ID:           requestID,
		SerialNumber: req.SerialNumber,
		Path:         req.Path,
		Priority:     priority,
		Status:       queue.StatusPending,
		Headers:      req.Headers,
		Metadata:     req.Metadata,
		CreatedAt:    time.Now(),
		MaxRetries:   3,
	}

	// Enqueue the request
	if err := s.queueManager.EnqueueRequest(ctx, queuedReq); err != nil {
		return nil, fmt.Errorf("failed to enqueue request: %w", err)
	}

	return &pb.DeviceResponse{
		RequestId: requestID,
		IsAsync:   true,
	}, nil
}

// generateRequestID creates a unique request ID
func generateRequestID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// convertPriority converts protobuf priority to queue priority
func convertPriority(pbPriority int32) queue.Priority {
	switch pbPriority {
	case 0:
		return queue.PriorityLow
	case 1:
		return queue.PriorityNormal
	case 2:
		return queue.PriorityHigh
	case 3:
		return queue.PriorityCritical
	default:
		return queue.PriorityNormal
	}
}

// EnqueueRequest handles async request enqueueing
func (s *Server) EnqueueRequest(ctx context.Context, req *pb.AsyncRequest) (*pb.AsyncResponse, error) {
	// Generate unique request ID
	requestID, err := generateRequestID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate request ID: %w", err)
	}

	// Convert protobuf priority to queue priority
	priority := convertPriority(req.Priority)

	// Create queued request
	queuedReq := &queue.QueuedRequest{
		ID:           requestID,
		SerialNumber: req.SerialNumber,
		Path:         req.Path,
		Priority:     priority,
		Status:       queue.StatusPending,
		Headers:      req.Headers,
		Metadata:     req.Metadata,
		CreatedAt:    time.Now(),
		MaxRetries:   3,
	}

	// Enqueue the request
	if err := s.queueManager.EnqueueRequest(ctx, queuedReq); err != nil {
		return nil, fmt.Errorf("failed to enqueue request: %w", err)
	}

	return &pb.AsyncResponse{
		RequestId:                  requestID,
		Status:                     string(queue.StatusPending),
		EstimatedCompletionSeconds: 60, // Default estimate
	}, nil
}

// GetRequestStatus retrieves the status of an async request
func (s *Server) GetRequestStatus(ctx context.Context, req *pb.StatusRequest) (*pb.StatusResponse, error) {
	response, err := s.queueManager.GetRequestStatus(ctx, req.RequestId)
	if err != nil {
		return nil, fmt.Errorf("failed to get request status: %w", err)
	}

	pbResponse := &pb.StatusResponse{
		RequestId:  response.RequestID,
		Status:     string(response.Status),
		StatusCode: response.StatusCode,
		Body:       response.Body,
		Headers:    response.Headers,
		Error:      response.Error,
		CreatedAt:  response.CreatedAt.Unix(),
	}

	if response.CompletedAt != nil {
		pbResponse.CompletedAt = response.CompletedAt.Unix()
	}

	return pbResponse, nil
}

// CancelRequest cancels a pending async request
func (s *Server) CancelRequest(ctx context.Context, req *pb.CancelRequestMessage) (*pb.CancelResponse, error) {
	err := s.queueManager.CancelRequest(ctx, req.RequestId)
	if err != nil {
		return &pb.CancelResponse{
			RequestId: req.RequestId,
			Cancelled: false,
			Message:   fmt.Sprintf("Failed to cancel request: %v", err),
		}, nil
	}

	return &pb.CancelResponse{
		RequestId: req.RequestId,
		Cancelled: true,
		Message:   "Request cancelled successfully",
	}, nil
}
