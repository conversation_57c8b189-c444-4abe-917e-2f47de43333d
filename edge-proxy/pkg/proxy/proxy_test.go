package proxy_test

import (
	"context"
	"fmt"
	"testing"

	. "github.com/EPIKio/myepikV2/edge/pkg/bdd"
	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	"github.com/EPIKio/myepikV2/edge/pkg/proxy"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// Test doubles
func fakeBox(serialNumber, datacenter, vpnAddr string) *boxes.Box {
	return &boxes.Box{
		SerialNumber: serialNumber,
		Datacenter:   datacenter,
		VPNAddress:   vpnAddr,
	}
}

func fakeResponse(status int, body string) *boxes.Response {
	return &boxes.Response{
		StatusCode: status,
		Body:       []byte(body),
		Headers:    map[string]string{"Content-Type": "application/json"},
	}
}

type boxMap map[string]*boxes.Box

// Our fake repository
type fakeBoxRepo struct {
	boxes boxMap
	err   error
}

// Compile-time interface check
var _ boxes.BoxFinder = (*fakeBoxRepo)(nil)

// Implementation of boxes.BoxRepository interface
func (f *fakeBoxRepo) FindBySerialNumber(ctx context.Context, sn string) (*boxes.Box, error) {
	if f.err != nil {
		return nil, f.err
	}
	return f.boxes[sn], nil
}

func newFakeBoxRepo(boxes ...*boxes.Box) *fakeBoxRepo {
	repo := &fakeBoxRepo{
		boxes: boxMap{},
	}
	for _, box := range boxes {
		repo.boxes[box.SerialNumber] = box
	}
	return repo
}

// Our fake client
type fakeClient struct {
	responses map[string]*boxes.Response
	err       error
}

// Compile-time interface check
var _ boxes.EdgeDeviceClient = (*fakeClient)(nil)

// Implementation of boxes.EdgeDeviceClient interface
func (f *fakeClient) Request(ctx context.Context, ip string, port boxes.ServicePort, path string, _method ...string) (*boxes.Response, error) {
	if f.err != nil {
		return nil, f.err
	}
	key := fmt.Sprintf("%s:%d%s", ip, int(port), path)
	return f.responses[key], nil
}

func newFakeClient() *fakeClient {
	return &fakeClient{
		responses: map[string]*boxes.Response{},
	}
}

func TestProxyServer(t *testing.T) {
	Scenario("Proxying requests to edge devices", t, func() {
		Given("a box in the LA datacenter", func() {
			laBox := fakeBox("000199140006", "LA", "************")
			boxRepo := newFakeBoxRepo(laBox)

			client := newFakeClient()
			client.responses["************:9988/dcavgping"] = fakeResponse(200, `{"status":"ok"}`)

			srv, err := proxy.NewServer(client, boxRepo)
			Check(err, ShouldBeNil)

			When("making a request to a valid endpoint", func() {
				statusCode, body, headers, err := srv.ProxyRequest(
					context.Background(),
					"000199140006",
					"/dcavgping",
				)

				Then("it should succeed", func() {
					Check(err, ShouldBeNil)
					Check(statusCode, ShouldEqual, 200)
					Check(string(body), ShouldEqual, `{"status":"ok"}`)
					Check(headers["Content-Type"], ShouldEqual, "application/json")
				})
			})
		})

		Given("a box in the New York datacenter", func() {
			nyBox := fakeBox("000199140007", "NY", "************")
			boxRepo := newFakeBoxRepo(nyBox)

			client := newFakeClient()
			// Note: IP is translated from 10.15.x.x to 10.19.x.x for NY
			client.responses["************:9988/dcavgping"] = fakeResponse(200, `{"status":"ok"}`)

			srv, err := proxy.NewServer(client, boxRepo)
			Check(err, ShouldBeNil)

			When("making a request", func() {
				statusCode, body, _, err := srv.ProxyRequest(
					context.Background(),
					"000199140007",
					"/dcavgping",
				)

				Then("it should translate the IP and succeed", func() {
					Check(err, ShouldBeNil)
					Check(statusCode, ShouldEqual, 200)
					Check(string(body), ShouldEqual, `{"status":"ok"}`)
				})
			})
		})

		Given("a non-existent box", func() {
			boxRepo := newFakeBoxRepo() // empty repo
			client := newFakeClient()

			srv, err := proxy.NewServer(client, boxRepo)
			Check(err, ShouldBeNil)

			When("making a request", func() {
				_, _, _, err := srv.ProxyRequest(
					context.Background(),
					"doesnotexist",
					"/dcavgping",
				)

				Then("it should return a NotFound error", func() {
					Check(err, ShouldNotBeNil)
					status, ok := status.FromError(err)
					Check(ok, ShouldBeTrue)
					Check(status.Code(), ShouldEqual, codes.NotFound)
				})
			})
		})

		Given("a box repository error", func() {
			boxRepo := newFakeBoxRepo()
			boxRepo.err = fmt.Errorf("database error")
			client := newFakeClient()

			srv, err := proxy.NewServer(client, boxRepo)
			Check(err, ShouldBeNil)

			When("making a request", func() {
				_, _, _, err := srv.ProxyRequest(
					context.Background(),
					"000199140006",
					"/dcavgping",
				)

				Then("it should return the error", func() {
					Check(err, ShouldNotBeNil)
					Check(err.Error(), ShouldContainSubstring, "database error")
				})
			})
		})

		Given("a client error", func() {
			laBox := fakeBox("000199140006", "LA", "************")
			boxRepo := newFakeBoxRepo(laBox)

			client := newFakeClient()
			client.err = fmt.Errorf("network error")

			srv, err := proxy.NewServer(client, boxRepo)
			Check(err, ShouldBeNil)

			When("making a request", func() {
				_, _, _, err := srv.ProxyRequest(
					context.Background(),
					"000199140006",
					"/dcavgping",
				)

				Then("it should return an Internal error", func() {
					Check(err, ShouldNotBeNil)
					status, ok := status.FromError(err)
					Check(ok, ShouldBeTrue)
					Check(status.Code(), ShouldEqual, codes.Internal)
					Check(status.Message(), ShouldContainSubstring, "failed to contact box")
				})
			})
		})
	})
}
