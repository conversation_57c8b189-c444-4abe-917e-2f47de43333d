package queue

import (
	"context"
	"time"
)

// Priority levels for queue messages
type Priority int

const (
	PriorityLow Priority = iota
	PriorityNormal
	PriorityHigh
	PriorityCritical
)

// String returns the string representation of priority
func (p Priority) String() string {
	switch p {
	case PriorityLow:
		return "low"
	case PriorityNormal:
		return "normal"
	case PriorityHigh:
		return "high"
	case PriorityCritical:
		return "critical"
	default:
		return "unknown"
	}
}

// RequestStatus represents the current status of a queued request
type RequestStatus string

const (
	StatusPending     RequestStatus = "pending"
	StatusProcessing  RequestStatus = "processing"
	StatusCompleted   RequestStatus = "completed"
	StatusFailed      RequestStatus = "failed"
	StatusRetrying    RequestStatus = "retrying"
	StatusCancelled   RequestStatus = "cancelled"
)

// QueuedRequest represents a request that has been queued for processing
type QueuedRequest struct {
	ID           string            `bson:"_id" json:"id"`
	SerialNumber string            `bson:"serial_number" json:"serial_number"`
	Path         string            `bson:"path" json:"path"`
	Priority     Priority          `bson:"priority" json:"priority"`
	Status       RequestStatus     `bson:"status" json:"status"`
	Headers      map[string]string `bson:"headers,omitempty" json:"headers,omitempty"`
	Metadata     map[string]string `bson:"metadata,omitempty" json:"metadata,omitempty"`
	
	// Timing information
	CreatedAt    time.Time  `bson:"created_at" json:"created_at"`
	ScheduledAt  *time.Time `bson:"scheduled_at,omitempty" json:"scheduled_at,omitempty"`
	StartedAt    *time.Time `bson:"started_at,omitempty" json:"started_at,omitempty"`
	CompletedAt  *time.Time `bson:"completed_at,omitempty" json:"completed_at,omitempty"`
	
	// Processing information
	RetryCount   int        `bson:"retry_count" json:"retry_count"`
	MaxRetries   int        `bson:"max_retries" json:"max_retries"`
	LastError    string     `bson:"last_error,omitempty" json:"last_error,omitempty"`
	
	// Response information (when completed)
	ResponseCode int               `bson:"response_code,omitempty" json:"response_code,omitempty"`
	ResponseBody []byte            `bson:"response_body,omitempty" json:"response_body,omitempty"`
	ResponseHeaders map[string]string `bson:"response_headers,omitempty" json:"response_headers,omitempty"`
}

// QueuedResponse represents the response for a queued request
type QueuedResponse struct {
	RequestID   string            `json:"request_id"`
	Status      RequestStatus     `json:"status"`
	StatusCode  int32             `json:"status_code,omitempty"`
	Body        []byte            `json:"body,omitempty"`
	Headers     map[string]string `json:"headers,omitempty"`
	Error       string            `json:"error,omitempty"`
	CreatedAt   time.Time         `json:"created_at"`
	CompletedAt *time.Time        `json:"completed_at,omitempty"`
}

// RequestProcessor handles processing of queued requests
type RequestProcessor interface {
	ProcessRequest(ctx context.Context, req *QueuedRequest) error
}

// QueueManager manages the request queue
type QueueManager interface {
	// Enqueue adds a request to the queue
	EnqueueRequest(ctx context.Context, req *QueuedRequest) error
	
	// GetRequestStatus retrieves the status of a request
	GetRequestStatus(ctx context.Context, requestID string) (*QueuedResponse, error)
	
	// CancelRequest cancels a pending request
	CancelRequest(ctx context.Context, requestID string) error
	
	// StartProcessing starts processing queued requests
	StartProcessing(ctx context.Context, processor RequestProcessor) error
	
	// Stop gracefully stops the queue manager
	Stop() error
}

// MessageState tracks the state of messages in the queue system
type MessageState struct {
	ID          string        `bson:"_id"`
	StreamName  string        `bson:"stream_name"`
	Subject     string        `bson:"subject"`
	Sequence    uint64        `bson:"sequence"`
	ConsumerID  string        `bson:"consumer_id"`
	Status      string        `bson:"status"` // pending, acked, nacked, redelivered
	Payload     []byte        `bson:"payload"`
	Headers     map[string]interface{} `bson:"headers"`
	Timestamp   time.Time     `bson:"timestamp"`
	AckDeadline time.Time     `bson:"ack_deadline"`
	RetryCount  int           `bson:"retry_count"`
	RequestID   string        `bson:"request_id"` // Link to QueuedRequest
}

// ConsumerOffset tracks consumer processing offsets
type ConsumerOffset struct {
	ID           string    `bson:"_id"`
	StreamName   string    `bson:"stream_name"`
	ConsumerName string    `bson:"consumer_name"`
	LastSequence uint64    `bson:"last_sequence"`
	UpdatedAt    time.Time `bson:"updated_at"`
}

// StreamConfig stores stream configuration in MongoDB
type StreamConfig struct {
	ID        string                 `bson:"_id"`
	Name      string                 `bson:"name"`
	Subjects  []string              `bson:"subjects"`
	Config    map[string]interface{} `bson:"config"`
	CreatedAt time.Time             `bson:"created_at"`
	UpdatedAt time.Time             `bson:"updated_at"`
}
