package queue_test

import (
	"context"
	"testing"
	"time"

	. "github.com/EPIKio/myepikV2/edge/pkg/bdd"
	"github.com/EPIKio/myepikV2/edge/pkg/queue"
)

// Mock processor for testing
type mockProcessor struct {
	processFunc func(ctx context.Context, req *queue.QueuedRequest) error
	callCount   int
}

func (m *mockProcessor) ProcessRequest(ctx context.Context, req *queue.QueuedRequest) error {
	m.callCount++
	if m.processFunc != nil {
		return m.processFunc(ctx, req)
	}
	return nil
}

func TestPriorityProcessor(t *testing.T) {
	Scenario("Processing requests with priority-based timeouts", t, func() {
		Given("a priority processor", func() {
			mockProc := &mockProcessor{}
			processor := queue.NewPriorityProcessor(mockProc)

			When("processing a critical priority request", func() {
				req := &queue.QueuedRequest{
					ID:           "test-critical",
					SerialNumber: "123456",
					Path:         "/test",
					Priority:     queue.PriorityCritical,
					Status:       queue.StatusPending,
					CreatedAt:    time.Now(),
				}

				err := processor.ProcessRequest(context.Background(), req)

				Then("it should process successfully", func() {
					Check(err, ShouldBeNil)
					Check(mockProc.callCount, ShouldEqual, 1)
				})
			})

			When("processing a low priority request", func() {
				req := &queue.QueuedRequest{
					ID:           "test-low",
					SerialNumber: "123456",
					Path:         "/test",
					Priority:     queue.PriorityLow,
					Status:       queue.StatusPending,
					CreatedAt:    time.Now(),
				}

				err := processor.ProcessRequest(context.Background(), req)

				Then("it should process successfully with longer timeout", func() {
					Check(err, ShouldBeNil)
					Check(mockProc.callCount, ShouldEqual, 2) // Previous call + this one
				})
			})
		})
	})
}

func TestRetryProcessor(t *testing.T) {
	Scenario("Processing requests with retry logic", t, func() {
		Given("a retry processor with max 2 retries", func() {
			mockProc := &mockProcessor{}
			processor := queue.NewRetryProcessor(mockProc, 2, 10*time.Millisecond)

			When("the underlying processor fails twice then succeeds", func() {
				failCount := 0
				mockProc.processFunc = func(ctx context.Context, req *queue.QueuedRequest) error {
					failCount++
					if failCount <= 2 {
						return context.DeadlineExceeded // Retryable error
					}
					return nil // Success on third attempt
				}

				req := &queue.QueuedRequest{
					ID:           "test-retry",
					SerialNumber: "123456",
					Path:         "/test",
					Priority:     queue.PriorityNormal,
					Status:       queue.StatusPending,
					CreatedAt:    time.Now(),
				}

				err := processor.ProcessRequest(context.Background(), req)

				Then("it should eventually succeed", func() {
					Check(err, ShouldBeNil)
					Check(mockProc.callCount, ShouldEqual, 3) // 2 failures + 1 success
				})
			})

			When("the underlying processor fails with non-retryable error", func() {
				mockProc.processFunc = func(ctx context.Context, req *queue.QueuedRequest) error {
					return context.Canceled // Non-retryable error
				}

				req := &queue.QueuedRequest{
					ID:           "test-no-retry",
					SerialNumber: "123456",
					Path:         "/test",
					Priority:     queue.PriorityNormal,
					Status:       queue.StatusPending,
					CreatedAt:    time.Now(),
				}

				err := processor.ProcessRequest(context.Background(), req)

				Then("it should fail immediately without retries", func() {
					Check(err, ShouldEqual, context.Canceled)
					Check(mockProc.callCount, ShouldEqual, 4) // Previous calls + this one
				})
			})
		})
	})
}

func TestBatchProcessor(t *testing.T) {
	Scenario("Processing multiple requests in batch", t, func() {
		Given("a batch processor", func() {
			mockProc := &mockProcessor{}
			processor := queue.NewBatchProcessor(mockProc, 5, time.Second)

			When("processing a batch of requests", func() {
				requests := []*queue.QueuedRequest{
					{
						ID:           "batch-1",
						SerialNumber: "123456",
						Path:         "/test1",
						Priority:     queue.PriorityNormal,
						Status:       queue.StatusPending,
						CreatedAt:    time.Now(),
					},
					{
						ID:           "batch-2",
						SerialNumber: "123457",
						Path:         "/test2",
						Priority:     queue.PriorityNormal,
						Status:       queue.StatusPending,
						CreatedAt:    time.Now(),
					},
					{
						ID:           "batch-3",
						SerialNumber: "123458",
						Path:         "/test3",
						Priority:     queue.PriorityNormal,
						Status:       queue.StatusPending,
						CreatedAt:    time.Now(),
					},
				}

				errors := processor.ProcessBatch(context.Background(), requests)

				Then("it should process all requests", func() {
					Check(len(errors), ShouldEqual, 3)
					for _, err := range errors {
						Check(err, ShouldBeNil)
					}
					Check(mockProc.callCount, ShouldEqual, 7) // Previous calls + 3 new ones
				})
			})
		})
	})
}

func TestMetricsProcessor(t *testing.T) {
	Scenario("Processing requests with metrics collection", t, func() {
		Given("a metrics processor", func() {
			mockProc := &mockProcessor{}
			processor := queue.NewMetricsProcessor(mockProc)

			When("processing a request", func() {
				req := &queue.QueuedRequest{
					ID:           "test-metrics",
					SerialNumber: "123456",
					Path:         "/test",
					Priority:     queue.PriorityNormal,
					Status:       queue.StatusPending,
					CreatedAt:    time.Now(),
				}

				err := processor.ProcessRequest(context.Background(), req)

				Then("it should process successfully and collect metrics", func() {
					Check(err, ShouldBeNil)
					Check(mockProc.callCount, ShouldEqual, 8) // Previous calls + this one
					// Note: In a real implementation, we would verify metrics were recorded
				})
			})
		})
	})
}

func TestRequestStatusTransitions(t *testing.T) {
	Scenario("Request status transitions", t, func() {
		Given("a queued request", func() {
			req := &queue.QueuedRequest{
				ID:           "status-test",
				SerialNumber: "123456",
				Path:         "/test",
				Priority:     queue.PriorityNormal,
				Status:       queue.StatusPending,
				CreatedAt:    time.Now(),
				MaxRetries:   3,
			}

			Then("it should start with pending status", func() {
				Check(req.Status, ShouldEqual, queue.StatusPending)
			})

			When("processing starts", func() {
				req.Status = queue.StatusProcessing
				req.StartedAt = &[]time.Time{time.Now()}[0]

				Then("it should be in processing status", func() {
					Check(req.Status, ShouldEqual, queue.StatusProcessing)
					Check(req.StartedAt, ShouldNotBeNil)
				})

				When("processing completes successfully", func() {
					req.Status = queue.StatusCompleted
					req.CompletedAt = &[]time.Time{time.Now()}[0]
					req.ResponseCode = 200
					req.ResponseBody = []byte(`{"status":"ok"}`)

					Then("it should be completed with response data", func() {
						Check(req.Status, ShouldEqual, queue.StatusCompleted)
						Check(req.CompletedAt, ShouldNotBeNil)
						Check(req.ResponseCode, ShouldEqual, 200)
						Check(string(req.ResponseBody), ShouldEqual, `{"status":"ok"}`)
					})
				})
			})
		})
	})
}

func TestPriorityLevels(t *testing.T) {
	Scenario("Priority level string representations", t, func() {
		Given("different priority levels", func() {
			priorities := []struct {
				priority queue.Priority
				expected string
			}{
				{queue.PriorityLow, "low"},
				{queue.PriorityNormal, "normal"},
				{queue.PriorityHigh, "high"},
				{queue.PriorityCritical, "critical"},
			}

			for _, p := range priorities {
				When("converting priority to string", func() {
					result := p.priority.String()

					Then("it should return the correct string representation", func() {
						Check(result, ShouldEqual, p.expected)
					})
				})
			}
		})
	})
}
