package db

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

type MongoClient struct {
	Client *mongo.Client
	DB     *mongo.Database
	Ctx    context.Context
}

var Client *mongo.Client
var DB *mongo.Database

func Connect(ctx context.Context, uri string) *MongoClient {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	docs := "www.mongodb.com/docs/drivers/go/current/"

	if uri == "" {
		fmt.Print("Set your 'MONGODB_URI' environment variable. " +
			"See: " + docs + "usage-examples/#environment-variable")
	}
	bsonOpts := &options.BSONOptions{
		UseJSONStructTags: true,
		NilSliceAsEmpty:   true,
	}

	client, err := mongo.
		Connect(ctx,
			options.Client().
				ApplyURI(uri).
				SetReadPreference(readpref.Secondary()).
				SetBSONOptions(bsonOpts),
		)

	if err != nil {
		fmt.Print("MongoDB connection failed: ", err)
	}

	if err := client.Ping(ctx, nil); err != nil {
		fmt.Print("MongoDB ping failed: ", err)
	}

	// Extract DB name
	dbNameMatch := regexp.MustCompile(`^mongodb(?:\+srv)?:\/\/[^/]+\/([^/?]+)`).FindStringSubmatch(uri)
	if len(dbNameMatch) < 2 {
		fmt.Print("Could not extract database name from URI")
	}
	dbName := dbNameMatch[1]
	Client = client
	DB = client.Database(dbName)
	fmt.Print("DB connected.")
	return &MongoClient{
		Client: client,
		DB:     client.Database(dbName),
		Ctx:    ctx,
	}
}
