# Edge Device Go Endpoints (EDGE) Proxy

A gRPC service that orchestrates complex device operations and proxies HTTP
requests to edge devices across multiple datacenters through VPN tunnels.
Part of the myepikV2 monorepo.

## Deployment
Automatically deployed to Kubernetes via GitHub Actions when changes are merged
to main.

## Features

### Core Functionality
- Complex device operation orchestration with multi-step workflows
- MongoDB-based address lookup with datacenter-specific VPN translation
- Support for multiple datacenter locations (LA, NY, CH, AT, DA/DL/DAL)
- Secure communication via gRPC and VPN tunnels
- Scalable handling of thousands of field devices
- Configurable through environment variables
- Graceful shutdown handling

### Enhanced Queue & Priority System
- **Asynchronous Request Processing**: Queue requests for background processing
- **Priority-Based Scheduling**: Critical, High, Normal, and Low priority levels
- **Persistent Message Storage**: MongoDB-backed request state tracking
- **JetStream Integration**: NATS JetStream for reliable message delivery
- **Automatic Retry Logic**: Configurable retry mechanisms for failed requests
- **Request Status Tracking**: Real-time status monitoring and updates
- **Recovery Mechanisms**: Automatic recovery of unacknowledged messages
- **Batch Processing**: Efficient handling of multiple requests
- **Metrics Collection**: Built-in performance and processing metrics

## Device Resolution

### MongoDB Lookup
Resolves device addresses using MongoDB, returning:
- VPN Address
- Datacenter Information

## Architecture

### Components
- **gRPC Service**: Handles incoming requests from web backends
- **Request Orchestrator**: Manages complex multi-step device operations
- **Box Repository**: MongoDB-backed device lookup service
- **HTTP Client**: Configurable client with retry logic
- **Proxy Logic**: Handles datacenter-specific VPN translation
- **Queue Manager**: JetStream-based message queue with MongoDB persistence
- **Priority Classifier**: Intelligent request priority assignment
- **Request Processor**: Handles async request processing with retry logic
- **Recovery System**: Monitors and recovers failed/stuck requests

### Data Flow
```mermaid
sequenceDiagram
    participant B as Web Backend
    participant E as EDGE Proxy
    participant M as MongoDB
    participant D as Edge Device

    B->>E: gRPC Request
    E->>M: Lookup Device
    M-->>E: VPN IP + Datacenter
    E->>E: Translate VPN IP
    E->>D: HTTP Request via VPN
    D-->>E: HTTP Response
    E->>E: Process Response
    E-->>B: gRPC Response
```

## Configuration

Environment variables:
```bash
# Server Configuration
EDGE_PORT=50051                                    # gRPC server port (default)
EDGE_MONGO_URI=mongodb://localhost:27017          # MongoDB connection URI
EDGE_MONGO_DB=epikFax                             # MongoDB database name

# Queue System Configuration
EDGE_NATS_URL=nats://localhost:4222               # NATS server URL
EDGE_QUEUE_STREAM=edge-requests                   # JetStream stream name
EDGE_QUEUE_CONSUMER=edge-processor                # Consumer name
EDGE_QUEUE_MAX_RETRIES=3                          # Maximum retry attempts
EDGE_QUEUE_ACK_TIMEOUT=30                         # Acknowledgment timeout (seconds)
EDGE_QUEUE_PROCESSING_TIMEOUT=300                 # Processing timeout (seconds)
```

## API

### gRPC Service

```protobuf
service EdgeDeviceProxy {
  // Synchronous or asynchronous request handling
  rpc HandleRequest(DeviceRequest) returns (DeviceResponse);

  // Explicit async request enqueueing
  rpc EnqueueRequest(AsyncRequest) returns (AsyncResponse);

  // Check status of async requests
  rpc GetRequestStatus(StatusRequest) returns (StatusResponse);

  // Cancel pending async requests
  rpc CancelRequest(CancelRequestMessage) returns (CancelResponse);
}

// Enhanced request message with async and priority support
message DeviceRequest {
  string serial_number = 1;                    // Device serial number
  string path = 2;                            // Request path
  int32 priority = 3;                         // Priority level (0-3)
  bool async = 4;                             // Process asynchronously
  map<string, string> headers = 5;            // Additional headers
  map<string, string> metadata = 6;           // Request metadata
}

// Async-specific request message
message AsyncRequest {
  string serial_number = 1;
  string path = 2;
  int32 priority = 3;
  map<string, string> headers = 4;
  map<string, string> metadata = 5;
}
```

### Priority Levels

The system supports four priority levels:

| Priority | Value | Description | Use Cases |
|----------|-------|-------------|-----------|
| **Critical** | 3 | Highest priority, processed immediately | Emergency operations, health checks, system alerts |
| **High** | 2 | High priority, minimal delay | Real-time data requests, user interactions |
| **Normal** | 1 | Standard priority | Regular API calls, routine operations |
| **Low** | 0 | Background processing | Batch operations, analytics, reports |

### Automatic Priority Classification

The system automatically classifies requests based on path patterns:

- **Critical**: `/emergency/*`, `/health`, `/ping`, `/status`, `/alarm/*`
- **High**: `/realtime/*`, `/live/*`, `/current/*`, user interactive requests
- **Normal**: `/api/*`, `/v1/*`, `/v2/*`, `/rest/*`
- **Low**: `/batch/*`, `/bulk/*`, `/export/*`, `/analytics/*`, `/backup/*`

## Usage Examples

### Synchronous Request
```go
// Standard synchronous request
response, err := client.HandleRequest(ctx, &pb.DeviceRequest{
    SerialNumber: "000199140006",
    Path:         "/dcavgping",
})
```

### Asynchronous Request
```go
// Async request with high priority
response, err := client.HandleRequest(ctx, &pb.DeviceRequest{
    SerialNumber: "000199140006",
    Path:         "/realtime/temperature",
    Priority:     2, // High priority
    Async:        true,
})

// Get the request ID for tracking
requestID := response.RequestId
```

### Check Request Status
```go
// Check status of async request
status, err := client.GetRequestStatus(ctx, &pb.StatusRequest{
    RequestId: requestID,
})

fmt.Printf("Status: %s\n", status.Status)
if status.Status == "completed" {
    fmt.Printf("Response: %s\n", string(status.Body))
}
```

### Cancel Request
```go
// Cancel a pending request
cancelResp, err := client.CancelRequest(ctx, &pb.CancelRequestMessage{
    RequestId: requestID,
})

if cancelResp.Cancelled {
    fmt.Println("Request cancelled successfully")
}
```

## Queue System Architecture

### Message Flow
```mermaid
sequenceDiagram
    participant C as Client
    participant G as gRPC Server
    participant Q as Queue Manager
    participant J as JetStream
    participant M as MongoDB
    participant P as Processor
    participant D as Edge Device

    C->>G: Async Request
    G->>Q: Enqueue Request
    Q->>M: Store Request State
    Q->>J: Publish Message
    G-->>C: Request ID

    J->>P: Deliver Message
    P->>M: Update Status (Processing)
    P->>D: HTTP Request
    D-->>P: HTTP Response
    P->>M: Update Status (Completed)
    P->>J: Acknowledge Message
```

### Persistence Strategy

The system uses a dual-persistence approach:

1. **JetStream (In-Memory)**: Fast message delivery and processing
2. **MongoDB**: Persistent request state, recovery, and audit trail

### Recovery Mechanisms

- **Message Recovery**: Automatic redelivery of unacknowledged messages
- **State Reconciliation**: Periodic cleanup of orphaned requests
- **Failure Handling**: Configurable retry logic with exponential backoff
- **Dead Letter Queue**: Failed requests after max retries

## Deployment

### Prerequisites
- **NATS Server** with JetStream enabled
- **MongoDB** instance for persistence
- **Go 1.23+** for building

### Docker Deployment
```yaml
version: '3.8'
services:
  nats:
    image: nats:latest
    command: ["-js", "-m", "8222"]
    ports:
      - "4222:4222"
      - "8222:8222"

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"

  edge-proxy:
    build: .
    environment:
      - EDGE_NATS_URL=nats://nats:4222
      - EDGE_MONGO_URI=mongodb://mongodb:27017
    ports:
      - "50051:50051"
    depends_on:
      - nats
      - mongodb
```

### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: edge-proxy
spec:
  replicas: 3
  selector:
    matchLabels:
      app: edge-proxy
  template:
    metadata:
      labels:
        app: edge-proxy
    spec:
      containers:
      - name: edge-proxy
        image: edge-proxy:latest
        env:
        - name: EDGE_NATS_URL
          value: "nats://nats.nats-system.svc.cluster.local:4222"
        - name: EDGE_MONGO_URI
          value: "mongodb://mongodb.default.svc.cluster.local:27017"
        ports:
        - containerPort: 50051
```

## Monitoring

### Key Metrics to Monitor

1. **Queue Metrics**
   - Queue depth by priority level
   - Processing throughput (requests/second)
   - Average processing time by priority
   - Failed request rate

2. **System Metrics**
   - Memory usage (JetStream buffers)
   - MongoDB connection pool status
   - gRPC request latency
   - Active consumer count

3. **Business Metrics**
   - Request success rate by device
   - Priority distribution
   - Peak processing times
   - SLA compliance by priority

### Health Checks

The service exposes health check endpoints:
- **gRPC Health Check**: Standard gRPC health protocol
- **Queue Health**: Monitor queue manager status
- **Database Health**: MongoDB connectivity check

### Logging

Structured logging includes:
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "component": "queue-processor",
  "request_id": "abc123",
  "serial_number": "000199140006",
  "priority": "high",
  "status": "completed",
  "duration_ms": 1250,
  "retry_count": 0
}
```

## Troubleshooting

### Common Issues

#### Queue Backlog
**Symptoms**: Increasing queue depth, slow response times
**Solutions**:
- Scale up processor instances
- Check device connectivity
- Review priority distribution
- Increase JetStream memory limits

#### Message Loss
**Symptoms**: Requests stuck in "processing" state
**Solutions**:
- Check MongoDB connectivity
- Review JetStream acknowledgment settings
- Verify consumer configuration
- Check recovery mechanism logs

#### High Memory Usage
**Symptoms**: OOM errors, slow performance
**Solutions**:
- Tune JetStream memory limits
- Implement message TTL
- Review queue retention policies
- Scale horizontally

### Debug Commands

```bash
# Check queue statistics
grpcurl -plaintext localhost:50051 edge.v1.EdgeDeviceProxy/GetRequestStatus

# Monitor JetStream
nats stream info edge-requests

# Check MongoDB collections
db.queued_requests.find({"status": "pending"}).count()
db.message_states.find({"status": "pending"}).count()
```

## Performance Tuning

### JetStream Configuration
- **Memory Storage**: Fast but limited by RAM
- **File Storage**: Persistent but slower
- **Replicas**: Balance availability vs performance
- **Max Age**: Prevent unbounded growth

### MongoDB Optimization
- **Indexes**: Ensure proper indexing on status, priority, created_at
- **Connection Pool**: Tune pool size for concurrent load
- **Write Concern**: Balance durability vs performance

### Processing Optimization
- **Batch Size**: Tune for optimal throughput
- **Timeout Values**: Balance responsiveness vs reliability
- **Retry Logic**: Exponential backoff with jitter

message DeviceResponse {
  int32 status_code = 1;
  bytes body = 2;
  map<string, string> headers = 3;
}
```

### Service Ports

- Restbin: 9988 (Main HTTP service)
- Watchguard: 9081 (Monitoring/repair agent)

## Development

### Prerequisites

- Go 1.23.3+
- protoc compiler
- gRPC tools

### Testing with grpcurl

1. Install grpcurl:
```bash
# MacOS
brew install grpcurl

# Linux
go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest

# Windows
# Download latest release from https://github.com/fullstorydev/grpcurl/releases
# Add the executable to your PATH
```

2. Send a test request:
```bash
grpcurl -plaintext -d '{"serial_number": "000199140006", "path": "/dcavgping"}' ^
  localhost:50051 edge.v1.EdgeDeviceProxy/HandleRequest
```

### Running Tests
```bash
go test -v ./...  # -v flag required to show BDD context in test failures
```

### CI/CD

CI runs automatically via GitHub Actions on pull requests and merges to main.
See `.github/workflows/go.yml` for the configuration.

### Deployment
Automatic deployment to Kubernetes is triggered on pushes to main. The service:
- Builds using the Dockerfile in `/edge`
- Pushes to epikedge/edge-proxy Docker registry
- Deploys via kubectl to the cluster

See `.github/workflows/deploy.yml` for details.

## VPN Address Translation

| Datacenter | 10.15.x.x | 10.64.x.x |
|------------|-----------|-----------|
| LA         | No change | No change |
| CH         | 10.18.x.x | 10.66.x.x |
| NY         | 10.19.x.x | 10.67.x.x |
| AT         | 10.20.x.x | 10.68.x.x |
| DA/DL/DAL  | 10.21.x.x | 10.69.x.x |

