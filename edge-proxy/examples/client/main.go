package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"time"

	pb "github.com/EPIKio/myepikV2/edge/proto/edge/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func main() {
	var (
		serverAddr   = flag.String("server", "localhost:50051", "gRPC server address")
		serialNumber = flag.String("serial", "000199140006", "Device serial number")
		path         = flag.String("path", "/dcavgping", "Request path")
		priority     = flag.Int("priority", 1, "Priority level (0=low, 1=normal, 2=high, 3=critical)")
		async        = flag.Bool("async", false, "Process asynchronously")
		requestID    = flag.String("status", "", "Check status of request ID")
		cancel       = flag.String("cancel", "", "Cancel request ID")
	)
	flag.Parse()

	// Connect to the server
	conn, err := grpc.Dial(*serverAddr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}
	defer conn.Close()

	client := pb.NewEdgeDeviceProxyClient(conn)
	ctx, _ := context.WithTimeout(context.Background(), 30*time.Second)

	// Handle different operations
	switch {
	case *requestID != "":
		checkStatus(ctx, client, *requestID)
	case *cancel != "":
		cancelRequest(ctx, client, *cancel)
	default:
		makeRequest(ctx, client, *serialNumber, *path, int32(*priority), *async)
	}
}

func makeRequest(ctx context.Context, client pb.EdgeDeviceProxyClient, serialNumber, path string, priority int32, async bool) {
	fmt.Printf("Making request to device %s, path %s (priority: %d, async: %t)\n",
		serialNumber, path, priority, async)

	req := &pb.DeviceRequest{
		SerialNumber: serialNumber,
		Path:         path,
		Priority:     priority,
		Async:        async,
		Headers: map[string]string{
			"User-Agent": "edge-proxy-example-client",
		},
		Metadata: map[string]string{
			"client_version": "1.0.0",
			"user_type":      "interactive",
		},
	}

	resp, err := client.HandleRequest(ctx, req)
	if err != nil {
		log.Fatalf("Request failed: %v", err)
	}

	if resp.IsAsync {
		fmt.Printf("✓ Request queued successfully\n")
		fmt.Printf("  Request ID: %s\n", resp.RequestId)
		fmt.Printf("  Use --status=%s to check progress\n", resp.RequestId)

		// Optionally wait and check status
		fmt.Println("\nWaiting 5 seconds then checking status...")
		time.Sleep(5 * time.Second)
		checkStatus(ctx, client, resp.RequestId)
	} else {
		fmt.Printf("✓ Synchronous request completed\n")
		fmt.Printf("  Status Code: %d\n", resp.StatusCode)
		fmt.Printf("  Response: %s\n", string(resp.Body))
		if len(resp.Headers) > 0 {
			fmt.Println("  Headers:")
			for k, v := range resp.Headers {
				fmt.Printf("    %s: %s\n", k, v)
			}
		}
	}
}

func checkStatus(ctx context.Context, client pb.EdgeDeviceProxyClient, requestID string) {
	fmt.Printf("Checking status of request: %s\n", requestID)

	statusReq := &pb.StatusRequest{
		RequestId: requestID,
	}

	status, err := client.GetRequestStatus(ctx, statusReq)
	if err != nil {
		log.Fatalf("Failed to get status: %v", err)
	}

	fmt.Printf("✓ Request Status: %s\n", status.Status)
	fmt.Printf("  Created: %s\n", time.Unix(status.CreatedAt, 0).Format(time.RFC3339))

	if status.CompletedAt > 0 {
		fmt.Printf("  Completed: %s\n", time.Unix(status.CompletedAt, 0).Format(time.RFC3339))
		duration := time.Unix(status.CompletedAt, 0).Sub(time.Unix(status.CreatedAt, 0))
		fmt.Printf("  Duration: %v\n", duration)
	}

	switch status.Status {
	case "completed":
		fmt.Printf("  Status Code: %d\n", status.StatusCode)
		fmt.Printf("  Response: %s\n", string(status.Body))
		if len(status.Headers) > 0 {
			fmt.Println("  Headers:")
			for k, v := range status.Headers {
				fmt.Printf("    %s: %s\n", k, v)
			}
		}
	case "failed":
		fmt.Printf("  Error: %s\n", status.Error)
	case "pending":
		fmt.Println("  Request is waiting to be processed")
	case "processing":
		fmt.Println("  Request is currently being processed")
	}
}

func cancelRequest(ctx context.Context, client pb.EdgeDeviceProxyClient, requestID string) {
	fmt.Printf("Cancelling request: %s\n", requestID)

	cancelReq := &pb.CancelRequestMessage{
		RequestId: requestID,
	}

	resp, err := client.CancelRequest(ctx, cancelReq)
	if err != nil {
		log.Fatalf("Failed to cancel request: %v", err)
	}

	if resp.Cancelled {
		fmt.Printf("✓ Request cancelled successfully\n")
	} else {
		fmt.Printf("✗ Failed to cancel request: %s\n", resp.Message)
	}
}

// Example usage:
//
// # Synchronous request
// go run main.go --serial=000199140006 --path=/dcavgping
//
// # Asynchronous high-priority request
// go run main.go --serial=000199140006 --path=/realtime/temperature --priority=2 --async=true
//
// # Check status of async request
// go run main.go --status=abc123def456
//
// # Cancel pending request
// go run main.go --cancel=abc123def456
//
// # Emergency request (will be auto-classified as critical)
// go run main.go --serial=000199140006 --path=/emergency/shutdown --async=true
//
// # Batch operation (will be auto-classified as low priority)
// go run main.go --serial=000199140006 --path=/batch/export --async=true
