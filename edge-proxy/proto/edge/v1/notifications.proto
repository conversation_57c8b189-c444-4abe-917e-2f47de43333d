syntax = "proto3";

package edge.v1;

option go_package = "github.com/EPIKio/myepikV2/edge/proto/edge/v1;edgev1";

// DeviceRequest represents a request to proxy to an edge device.
message DeviceRequest {
  // Serial number of the device
  string serial_number = 1;

  // Path to forward to the device (e.g., "/dcavgping")
  string path = 2;

  // Optional: Request priority (0=low, 1=normal, 2=high, 3=critical)
  int32 priority = 3;

  // Optional: Process asynchronously and return request ID
  bool async = 4;

  // Optional: Additional headers to send with the request
  map<string, string> headers = 5;

  // Optional: Metadata for request classification
  map<string, string> metadata = 6;
}

// Response from the edge device
message DeviceResponse {
  // HTTP status code from the device
  int32 status_code = 1;

  // Raw response body from the device
  bytes body = 2;

  // HTTP headers from the device's response
  map<string, string> headers = 3;

  // Request ID for async requests
  string request_id = 4;

  // Indicates if this is an async response (contains only request_id)
  bool is_async = 5;
}

// AsyncRequest represents a request for async processing
message AsyncRequest {
  // Serial number of the device
  string serial_number = 1;

  // Path to forward to the device
  string path = 2;

  // Request priority (0=low, 1=normal, 2=high, 3=critical)
  int32 priority = 3;

  // Additional headers to send with the request
  map<string, string> headers = 4;

  // Metadata for request classification
  map<string, string> metadata = 5;
}

// AsyncResponse contains the request ID for tracking
message AsyncResponse {
  // Unique request ID for tracking
  string request_id = 1;

  // Current status of the request
  string status = 2;

  // Estimated processing time (if available)
  int64 estimated_completion_seconds = 3;
}

// StatusRequest for checking async request status
message StatusRequest {
  // Request ID to check
  string request_id = 1;
}

// StatusResponse contains the current status and result (if completed)
message StatusResponse {
  // Request ID
  string request_id = 1;

  // Current status (pending, processing, completed, failed, cancelled)
  string status = 2;

  // HTTP status code (if completed)
  int32 status_code = 3;

  // Response body (if completed)
  bytes body = 4;

  // Response headers (if completed)
  map<string, string> headers = 5;

  // Error message (if failed)
  string error = 6;

  // Timestamps
  int64 created_at = 7;
  int64 completed_at = 8;
}

// CancelRequestMessage for cancelling pending async requests
message CancelRequestMessage {
  // Request ID to cancel
  string request_id = 1;
}

// CancelResponse confirms cancellation
message CancelResponse {
  // Request ID that was cancelled
  string request_id = 1;

  // Whether cancellation was successful
  bool cancelled = 2;

  // Message about cancellation status
  string message = 3;
}

// EdgeDeviceProxy service definition
service EdgeDeviceProxy {
  // HandleRequest handles proxying requests to edge devices (sync or async)
  rpc HandleRequest(DeviceRequest) returns (DeviceResponse) {}

  // EnqueueRequest queues a request for async processing
  rpc EnqueueRequest(AsyncRequest) returns (AsyncResponse) {}

  // GetRequestStatus checks the status of an async request
  rpc GetRequestStatus(StatusRequest) returns (StatusResponse) {}

  // CancelRequest cancels a pending async request
  rpc CancelRequest(CancelRequestMessage) returns (CancelResponse) {}
}
