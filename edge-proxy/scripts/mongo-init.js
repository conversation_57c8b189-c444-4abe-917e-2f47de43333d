// MongoDB initialization script for edge-proxy
// This script sets up the database schema and sample data

// Switch to the application database
db = db.getSiblingDB('epikFax');

db.createCollection('queued_requests', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['_id', 'serial_number', 'path', 'priority', 'status', 'created_at'],
      properties: {
        _id: {
          bsonType: 'string',
          description: 'Unique request identifier'
        },
        serial_number: {
          bsonType: 'string',
          description: 'Device serial number'
        },
        path: {
          bsonType: 'string',
          description: 'Request path'
        },
        priority: {
          bsonType: 'int',
          minimum: 0,
          maximum: 3,
          description: 'Priority level (0-3)'
        },
        status: {
          bsonType: 'string',
          enum: ['pending', 'processing', 'completed', 'failed', 'retrying', 'cancelled'],
          description: 'Request status'
        },
        created_at: {
          bsonType: 'date',
          description: 'Request creation timestamp'
        }
      }
    }
  }
});

db.createCollection('message_states');
db.createCollection('consumer_offsets');
db.createCollection('stream_configs');

// Create indexes for optimal performance
print('Creating indexes...');

// epikboxes indexes
db.epikboxes.createIndex({ 'serialNumber': 1 }, { unique: true });
db.epikboxes.createIndex({ 'datacenter': 1 });
db.epikboxes.createIndex({ 'vpnAddress': 1 });

// queued_requests indexes
db.queued_requests.createIndex({ 'status': 1, 'priority': -1, 'created_at': 1 });
db.queued_requests.createIndex({ 'serial_number': 1 });
db.queued_requests.createIndex({ 'created_at': 1 });
db.queued_requests.createIndex({ 'status': 1 });
db.queued_requests.createIndex({ 'priority': 1 });

// message_states indexes
db.message_states.createIndex({ 'stream_name': 1, 'sequence': 1 });
db.message_states.createIndex({ 'consumer_id': 1, 'status': 1 });
db.message_states.createIndex({ 'ack_deadline': 1 });
db.message_states.createIndex({ 'request_id': 1 });

// consumer_offsets indexes
db.consumer_offsets.createIndex({ 'stream_name': 1, 'consumer_name': 1 }, { unique: true });

// stream_configs indexes
db.stream_configs.createIndex({ 'name': 1 }, { unique: true });


print('Indexes created successfully!');